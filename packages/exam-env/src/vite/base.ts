import type { UserConfig } from 'vite';
import path from 'node:path';
import { BossDesignResolver } from '@boss/design-resolver';
import inject from '@rollup/plugin-inject';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite';
import components from 'unplugin-vue-components/vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

// @ts-ignore
import SetSourceMapFiletToPathPlugin from 'sourcemap-set-path-plugin';
const sourceDir = path.resolve(__dirname, './dist/');
const targetDir = path.resolve(__dirname, './dist/map/');
/**
 * 获取基础 vite 配置
 * @param cwd 当前工作目录
 * @param mode 构建模式
 * @param aliasConfig 共享的别名配置
 * @returns 基础配置
 */
export function getBaseConfig(cwd: string, mode: string, aliasConfig: any[] = []): UserConfig {
    return {
        plugins: [
            vue(),
            inject({
                Invoke: path.resolve(cwd, 'src/services/invoke.ts'),
            }),
            vueJsx(),
            createSvgIconsPlugin({
                iconDirs: [path.resolve(cwd, 'src/assets/svg')],
                symbolId: 'icon-[dir]-[name]',
            }),
            components({
                resolvers: [BossDesignResolver()],
            }),
            AutoImport({
                resolvers: [BossDesignResolver({ autoImport: true })],
            }),
            {
                ...SetSourceMapFiletToPathPlugin({ sourceDir, targetDir }),
                enforce: 'post',
                apply: 'build',
            },
        ],
        base: '/',
        define: {
            // 将构建时的 mode 注入到运行时
            __VITE_BUILD_MODE__: JSON.stringify(mode),
            // 保持原有的部署版本注入
            __DEPLOY_VERSION__: JSON.stringify(process.env.VITE_DEPLOY_VERSION || ''),
        },
        resolve: {
            alias: [...aliasConfig, { find: '@', replacement: path.resolve(cwd, 'src') }, { find: '@/exam-env', replacement: path.resolve(cwd, 'src/exam-env') }],
        },
        optimizeDeps: {
            // 排除问题依赖
            exclude: ['fsevents'],
        },
        esbuild: {
            // 只在生产模式下移除 console 和 debugger
            drop: mode === 'prod' ? ['console', 'debugger'] : undefined,
        },
        css: {
            preprocessorOptions: {
                less: {
                    math: 'always',
                },
            },
        },
        build: {
            sourcemap: true,
            assetsDir: 'static',
            assetsInlineLimit: 1024,
            rollupOptions: {
                onwarn(warning, warn) {
                    // 忽略来自第三方库的 eval 警告
                    if (warning.code === 'EVAL' && warning.id?.includes('node_modules')) {
                        return;
                    }
                    warn(warning);
                },
            },
        },
        server: {
            host: true,
            cors: true,
        },
    };
}
